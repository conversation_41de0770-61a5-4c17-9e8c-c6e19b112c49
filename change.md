# 🚀 Kế hoạch Modernization - Qu<PERSON>n lý <PERSON>hiết bị

## 📋 Tổng quan
Kế hoạch nâng cấp toàn diện dự án từ stack hiệ<PERSON> tại sang modern stack với TanStack ecosystem, Cloudflare D1, và Bun.

### 🎯 Mục tiêu ch<PERSON>h
- ✅ Giữ nguyên giao diện và UX hiện tại
- 🔄 Modernize tech stack
- 🚀 Cải thiện performance và DX
- 🛡️ Tăng cường type safety

---

## 📦 Phase 1: Package Manager Migration (Bun)

### 🎯 Mục tiêu
Chu<PERSON>ển từ npm/pnpm sang Bun để cải thiện tốc độ build và install.

### ✅ Tasks
- [ ] **1.1** Cài đặt Bun globally
  ```bash
  curl -fsSL https://bun.sh/install | bash
  ```

- [ ] **1.2** Backup current lock files
  ```bash
  cp package-lock.json package-lock.json.backup
  cp bun.lockb bun.lockb.backup
  ```

- [ ] **1.3** Remove old lock files và node_modules
  ```bash
  rm -rf node_modules package-lock.json
  ```

- [ ] **1.4** Install dependencies với Bun
  ```bash
  bun install
  ```

- [ ] **1.5** Update package.json scripts
  ```json
  {
    "scripts": {
      "dev": "bun run vite",
      "build": "bun run vite build",
      "build:dev": "bun run vite build --mode development",
      "lint": "bun run eslint .",
      "preview": "bun run vite preview"
    }
  }
  ```

- [ ] **1.6** Test build và dev server
  ```bash
  bun run dev
  bun run build
  ```

### 🧪 Testing
- [ ] Verify app starts correctly
- [ ] Check all features work as before
- [ ] Verify build output

---

## 📚 Phase 2: Dependencies Update

### 🎯 Mục tiêu
Update tất cả dependencies lên phiên bản mới nhất.

### ✅ Tasks
- [ ] **2.1** Update React ecosystem
  ```bash
  bun add react@latest react-dom@latest
  bun add -d @types/react@latest @types/react-dom@latest
  ```

- [ ] **2.2** Update Vite và build tools
  ```bash
  bun add -d vite@latest @vitejs/plugin-react-swc@latest
  bun add -d typescript@latest
  ```

- [ ] **2.3** Update TanStack Query
  ```bash
  bun add @tanstack/react-query@latest
  ```

- [ ] **2.4** Update UI libraries
  ```bash
  bun add lucide-react@latest
  bun add class-variance-authority@latest clsx@latest
  bun add tailwind-merge@latest
  ```

- [ ] **2.5** Update Radix UI components
  ```bash
  bun add @radix-ui/react-accordion@latest @radix-ui/react-alert-dialog@latest
  # ... (update all @radix-ui packages)
  ```

### 🧪 Testing
- [ ] Run `bun run dev` và check console errors
- [ ] Test all UI components
- [ ] Verify TypeScript compilation

---

## 🔄 Phase 3: TanStack Migration

### 🎯 Mục tiêu
Migrate sang TanStack ecosystem (Router, Form, Table).

### ✅ Tasks

#### 3.1 TanStack Router Migration
- [ ] **3.1.1** Install TanStack Router
  ```bash
  bun add @tanstack/react-router
  bun add -d @tanstack/router-vite-plugin
  ```

- [ ] **3.1.2** Update vite.config.ts
  ```typescript
  import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
  
  export default defineConfig({
    plugins: [react(), TanStackRouterVite()],
  })
  ```

- [ ] **3.1.3** Create route tree structure
  ```
  src/routes/
  ├── __root.tsx
  ├── index.tsx
  └── _404.tsx
  ```

- [ ] **3.1.4** Migrate App.tsx to use TanStack Router
- [ ] **3.1.5** Update routing logic in components

#### 3.2 TanStack Form Migration
- [ ] **3.2.1** Install TanStack Form
  ```bash
  bun add @tanstack/react-form
  ```

- [ ] **3.2.2** Migrate React Hook Form usage
- [ ] **3.2.3** Update form validation với Zod integration

#### 3.3 TanStack Table (if needed)
- [ ] **3.3.1** Install TanStack Table
  ```bash
  bun add @tanstack/react-table
  ```

- [ ] **3.3.2** Create table components for equipment lists
- [ ] **3.3.3** Add sorting, filtering, pagination

### 🧪 Testing
- [ ] Test navigation between pages
- [ ] Test form submissions
- [ ] Test table interactions

---

## 🎨 Phase 4: Zod v4 & Tailwind v4 Migration

### 🎯 Mục tiêu
Upgrade Zod và Tailwind lên phiên bản mới nhất.

### ✅ Tasks

#### 4.1 Zod v4 Migration
- [ ] **4.1.1** Update Zod
  ```bash
  bun add zod@latest
  ```

- [ ] **4.1.2** Review breaking changes
- [ ] **4.1.3** Update schema definitions
- [ ] **4.1.4** Test form validations

#### 4.2 Tailwind v4 Migration
- [ ] **4.2.1** Update Tailwind CSS
  ```bash
  bun add -d tailwindcss@latest autoprefixer@latest postcss@latest
  ```

- [ ] **4.2.2** Update tailwind.config.ts for v4 syntax
- [ ] **4.2.3** Review CSS classes for breaking changes
- [ ] **4.2.4** Update @tailwindcss/typography if needed

### 🧪 Testing
- [ ] Verify all styling remains intact
- [ ] Test responsive design
- [ ] Check form validations

---

## 🗄️ Phase 5: Backend Setup (D1 + Hono + Drizzle)

### 🎯 Mục tiêu
Setup backend với Cloudflare D1, Hono, và Drizzle ORM.

### ✅ Tasks

#### 5.1 Cloudflare Setup
- [ ] **5.1.1** Install Wrangler CLI
  ```bash
  bun add -d wrangler
  ```

- [ ] **5.1.2** Create wrangler.toml
- [ ] **5.1.3** Setup D1 database
  ```bash
  bunx wrangler d1 create quan-ly-ttb-db
  ```

#### 5.2 Hono API Setup
- [ ] **5.2.1** Install Hono
  ```bash
  bun add hono
  ```

- [ ] **5.2.2** Create API structure
  ```
  src/api/
  ├── index.ts
  ├── routes/
  │   ├── walkie-talkies.ts
  │   ├── access-cards.ts
  │   └── history.ts
  └── middleware/
  ```

- [ ] **5.2.3** Setup CORS và middleware

#### 5.3 Drizzle ORM Setup
- [ ] **5.3.1** Install Drizzle
  ```bash
  bun add drizzle-orm
  bun add -d drizzle-kit
  ```

- [ ] **5.3.2** Create database schema
  ```
  src/db/
  ├── schema.ts
  ├── migrations/
  └── seed.ts
  ```

- [ ] **5.3.3** Setup migrations
- [ ] **5.3.4** Create seed data từ mock data hiện tại

#### 5.4 Integration
- [ ] **5.4.1** Update TanStack Query để call APIs
- [ ] **5.4.2** Replace mock data với real API calls
- [ ] **5.4.3** Add error handling và loading states

### 🧪 Testing
- [ ] Test API endpoints
- [ ] Test database operations
- [ ] Test frontend integration

---

## 🧪 Phase 6: Final Testing & Optimization

### ✅ Tasks
- [ ] **6.1** End-to-end testing
- [ ] **6.2** Performance optimization
- [ ] **6.3** Bundle size analysis
- [ ] **6.4** Accessibility testing
- [ ] **6.5** Mobile responsiveness
- [ ] **6.6** Error boundary setup
- [ ] **6.7** Production build testing

### 📊 Success Metrics
- [ ] App loads < 2s
- [ ] All features work as before
- [ ] No console errors
- [ ] TypeScript compilation success
- [ ] Bundle size không tăng đáng kể

---

## 🔄 Rollback Plan

### Nếu có issues:
1. **Phase 1**: Restore package-lock.json, remove bun.lockb
2. **Phase 2**: Downgrade specific packages
3. **Phase 3**: Keep React Router DOM as fallback
4. **Phase 4**: Revert to previous versions
5. **Phase 5**: Use mock data temporarily

### Emergency Commands:
```bash
# Restore npm
rm bun.lockb && npm install

# Revert git changes
git checkout -- package.json
git clean -fd
```

---

## 📝 Notes
- Mỗi phase nên được test kỹ trước khi chuyển sang phase tiếp theo
- Commit code sau mỗi phase thành công
- Backup database trước khi migrate
- Document breaking changes và solutions
